import React, { useEffect } from 'react';
import { AnalyticsLayout } from '@/components/Analytics';
import { useScreenReader } from '@/hooks/useAccessibility';

const TeacherAnalytics: React.FC = () => {
  const { announce } = useScreenReader();

  useEffect(() => {
    // Announce page load for screen readers
    announce('Analytics dashboard loaded. You can now view your teaching performance metrics and insights.');
  }, [announce]);

  return (
    <>
      <Helmet>
        <title>Analytics Dashboard - Teacher Dashboard | LMS Platform</title>
        <meta 
          name="description" 
          content="View comprehensive analytics about your courses, student engagement, revenue, and teaching performance in your teacher dashboard." 
        />
        <meta name="keywords" content="analytics, dashboard, teacher metrics, course performance, student engagement, revenue analytics" />
      </Helmet>

      <div className="h-screen overflow-hidden">
        <AnalyticsLayout />
      </div>
    </>
  );
};

export default TeacherAnalytics;
